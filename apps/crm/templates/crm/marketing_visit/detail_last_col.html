<!-- Enhanced styling for third column -->

<div class="card-body h-100 overflow-auto p-2">
    <!-- Visit Details Section -->
    <div class="visit-details p-2">
       
        <!-- Visit Information Card -->
        <div class="card mb-3 shadow-sm border-0">
           
            <div class="card-body p-3">
                <div class="table-responsive">
                    <table class="table table-borderless mb-0">
                        <tbody>
                            <tr class="border-bottom border-light">
                                <th class="fw-bold text-dark" style="width: 35%;">
                                    <i class="fas fa-hospital text-primary me-2"></i>병원명
                                </th>
                                <td class="text-dark fw-medium">{{ selected_visit.location_name.hospital_name }}</td>
                            </tr>
                            <tr class="border-bottom border-light">
                                <th class="fw-bold text-dark">
                                    <i class="fas fa-calendar-alt text-primary me-2"></i>방문 일자
                                </th>
                                <td class="text-dark fw-medium">{{ selected_visit.visit_date|date:"Y/m/d" }}</td>
                            </tr>
                            <tr class="border-bottom border-light">
                                <th class="fw-bold text-dark">
                                    <i class="fas fa-user-tie text-primary me-2"></i>면담 대상자
                                </th>
                                <td class="text-dark fw-medium">{{ selected_visit.person_met }}</td>
                            </tr>
                            <tr>
                                <th class="fw-bold text-dark">
                                    <i class="fas fa-file-alt text-primary me-2"></i>자료 제공
                                </th>
                                <td class="text-dark fw-medium">{{ selected_visit.files_provided }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Discussion Details Card -->
        <div class="card mb-3 shadow-sm border-0">
            <div class="card-header bg-success border-0">
                <h5 class="mb-2 text-white fw-bold">
                    <i class="fas fa-comment-alt me-2 text-white"></i>{{ selected_visit.main_topic }}
                </h5>
                <div class="d-flex flex-wrap gap-2 small">
                    {% if selected_visit.marketing_manager %}
                    <span class="badge bg-light text-dark border">
                        <i class="fas fa-user text-primary me-1"></i>
                        <strong class="text-primary">{{ selected_visit.marketing_manager }}</strong>
                    </span>
                    {% endif %}
                    <span class="badge bg-light text-dark border">
                        <i class="far fa-calendar-alt text-info me-1"></i>
                        {{ selected_visit.created_at|date:"Y.m.d" }}
                    </span>
                    {% if selected_visit.edit_history %}
                    <span class="badge bg-warning text-dark border">
                        <i class="fas fa-history me-1"></i>
                        수정: {{ selected_visit.updated_at|date:"Y.m.d" }}
                    </span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body p-3">
                <div class="lh-base text-dark fs-6 fw-medium">
                    {{ selected_visit.discussion_details|safe }}
                </div>
            </div>
        </div>

        <!-- Attachments Section -->
        {% if selected_visit.attachment %}
        <div class="card mb-3 shadow-sm border-0">
            <div class="card-header bg-warning border-0">
                <h5 class="mb-0 text-dark fw-bold">
                    <i class="fas fa-paperclip me-2 text-dark"></i>주요 첨부 파일
                </h5>
            </div>
            <div class="card-body p-3">
                <div class="d-flex align-items-center">
                    <i class="fas fa-file text-primary me-3 fs-3"></i>
                    <div class="flex-grow-1">
                        <p class="mb-2 fw-bold text-dark fs-6">{{ selected_visit.attachment_description }}</p>
                        <a href="{{ selected_visit.attachment.url }}" class="btn btn-sm btn-outline-primary shadow-sm" target="_blank">
                            <i class="fas fa-download me-1"></i>다운로드
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Additional Files Section -->
        <div class="card mb-3 shadow-sm border-0">
            <div class="card-header bg-info border-0 d-flex justify-content-between align-items-center">
                <h5 class="mb-0 text-white fw-bold">
                    <i class="fas fa-folder me-2 text-white"></i>추가 파일 목록
                </h5>
                {% if selected_visit.marketing_manager == request.user.user_employee or request.user.is_superuser %}
                <a href="{% url 'marketing_visit_update' selected_visit.id %}" class="btn btn-sm btn-light shadow-sm">
                    <i class="fas fa-edit me-1"></i>파일 관리
                </a>
                {% endif %}
            </div>
            <div class="card-body p-0">
                {% if selected_visit.files.all %}
                <div class="list-group list-group-flush">
                    {% for file in selected_visit.files.all %}
                    <div class="list-group-item border-0 border-bottom border-light">
                        <div class="d-flex justify-content-between align-items-center p-2">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file text-primary me-3 fs-4"></i>
                                <div>
                                    <p class="mb-1 fw-bold text-dark fs-6">{{ file.get_file_name }}</p>
                                    <small class="text-muted d-block fw-medium">{{ file.description }}</small>
                                    <small class="text-muted">{{ file.created_at|date:"Y-m-d H:i" }}</small>
                                </div>
                            </div>
                            <a href="{{ file.file.url }}" class="btn btn-sm btn-outline-primary shadow-sm" target="_blank">
                                <i class="fas fa-download me-1"></i>다운로드
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4 text-muted">
                    <i class="fas fa-info-circle fs-3 mb-3 text-info"></i>
                    <p class="mb-0 fw-medium">추가 파일이 없습니다.</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Comments Section -->
        {% include 'crm/marketing_visit/comments_with_scrollable.html' %}

        <!-- Edit History -->
        {% if selected_visit.edit_history %}
        <div class="card shadow-sm border-0">
            <div class="card-header bg-secondary border-0">
                <h5 class="mb-0 text-white fw-bold">
                    <i class="fas fa-history me-2 text-white"></i>수정 기록
                </h5>
            </div>
            <div class="card-body p-3">
                <div class="small">
                    {% for edit in selected_visit.edit_history reversed %}
                    <div class="py-2 border-bottom border-light d-flex justify-content-between align-items-center">
                        <span class="text-primary fw-bold fs-6">
                            <i class="fas fa-user me-2"></i>{{ edit.editor_name }}
                        </span>
                        <span class="text-muted fw-medium">{{ edit.edited_at|date:"Y-m-d H:i" }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- JavaScript is handled in the main list_detail.html file -->
