{% block extra_css %}
<style>
    /* Minimal custom styles for specific enhancements */
    .visit-details .card {
        border-color: rgba(0,0,0,0.08);
        transition: box-shadow 0.2s ease;
    }

    .visit-details .card:hover {
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .visit-details .table th {
        background-color: #f8f9fa;
        border-right: 1px solid rgba(0,0,0,0.05);
    }

    .visit-details .list-group-item {
        border-left: none;
        border-right: none;
    }

    .visit-details .list-group-item:hover {
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

<div class="card-body h-100 overflow-auto">
    <!-- Visit Details Section -->
    <div class="visit-details p-3">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="mb-0">
                <i class="fas fa-info-circle text-primary"></i> 방문 상세 정보
            </h4>
            <div class="d-flex gap-2">
                {% if request.user.is_superuser %}
                <a href="{% url 'marketing_visit_update' selected_visit.id %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-edit"></i> 수정
                </a>
                {% endif %}
            </div>
        </div>

        <!-- Visit Information Card -->
        <div class="card mb-3">
            <div class="card-header bg-white py-2">
                <h5 class="mb-0">
                    <i class="fas fa-hospital text-primary"></i> 방문 정보
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-borderless mb-0">
                        <tbody>
                            <tr>
                                <th class="text-muted" style="width: 35%;">병원명</th>
                                <td class="fw-bold">{{ selected_visit.location_name.hospital_name }}</td>
                            </tr>
                            <tr>
                                <th class="text-muted">방문 일자</th>
                                <td>{{ selected_visit.visit_date|date:"Y/m/d" }}</td>
                            </tr>
                            <tr>
                                <th class="text-muted">면담 대상자</th>
                                <td>{{ selected_visit.person_met }}</td>
                            </tr>
                            <tr>
                                <th class="text-muted">자료 제공</th>
                                <td>{{ selected_visit.files_provided }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Discussion Details Card -->
        <div class="card mb-3">
            <div class="card-header bg-white py-2">
                <h5 class="mb-2">
                    <i class="fas fa-comment-alt text-primary"></i> 
                    <span class="text-dark">{{ selected_visit.main_topic }}</span>
                </h5>
                <div class="d-flex flex-wrap gap-2 small text-muted">
                    {% if selected_visit.marketing_manager %}
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-user text-primary"></i>
                        <strong class="text-info">{{ selected_visit.marketing_manager }}</strong>
                    </span>
                    {% endif %}
                    <span class="badge bg-light text-dark">
                        <i class="far fa-calendar-alt"></i>
                        {{ selected_visit.created_at|date:"Y.m.d" }}
                    </span>
                    {% if selected_visit.edit_history %}
                    <span class="badge bg-light text-danger">
                        <i class="fas fa-history"></i>
                        마지막 수정: {{ selected_visit.updated_at|date:"Y.m.d" }}
                    </span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="lh-base text-dark">
                    {{ selected_visit.discussion_details|safe }}
                </div>
            </div>
        </div>

        <!-- Attachments Section -->
        {% if selected_visit.attachment %}
        <div class="card mb-3">
            <div class="card-header bg-white py-2">
                <h5 class="mb-0">
                    <i class="fas fa-paperclip text-primary"></i> 주요 첨부 파일
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <i class="fas fa-file text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                        <p class="mb-2 fw-bold">{{ selected_visit.attachment_description }}</p>
                        <a href="{{ selected_visit.attachment.url }}" class="btn btn-sm btn-outline-primary" target="_blank">
                            <i class="fas fa-download"></i> 다운로드
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Additional Files Section -->
        <div class="card mb-3">
            <div class="card-header bg-white py-2 d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-folder text-primary"></i> 추가 파일 목록
                </h5>
                {% if selected_visit.marketing_manager == request.user.user_employee or request.user.is_superuser %}
                <a href="{% url 'marketing_visit_update' selected_visit.id %}" class="btn btn-sm btn-outline-info">
                    <i class="fas fa-edit"></i> 파일 관리
                </a>
                {% endif %}
            </div>
            <div class="card-body p-0">
                {% if selected_visit.files.all %}
                <div class="list-group list-group-flush">
                    {% for file in selected_visit.files.all %}
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file text-primary me-3 fs-4"></i>
                                <div>
                                    <p class="mb-1 fw-bold">{{ file.get_file_name }}</p>
                                    <small class="text-muted d-block">{{ file.description }}</small>
                                    <small class="text-muted">{{ file.created_at|date:"Y-m-d H:i" }}</small>
                                </div>
                            </div>
                            <a href="{{ file.file.url }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                <i class="fas fa-download"></i>
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-3 text-muted">
                    <i class="fas fa-info-circle fs-4 mb-2"></i>
                    <p class="mb-0">추가 파일이 없습니다.</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Comments Section -->
        {% include 'crm/marketing_visit/comments_with_scrollable.html' %}

        <!-- Edit History -->
        {% if selected_visit.edit_history %}
        <div class="card">
            <div class="card-header bg-white py-2">
                <h5 class="mb-0">
                    <i class="fas fa-history text-primary"></i> 수정 기록
                </h5>
            </div>
            <div class="card-body">
                <div class="small">
                    {% for edit in selected_visit.edit_history reversed %}
                    <div class="py-1 border-bottom border-light">
                        <span class="text-success fw-bold">{{ edit.editor_name }}</span>
                        <span class="text-muted ms-2">{{ edit.edited_at|date:"Y-m-d H:i" }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- JavaScript is handled in the main list_detail.html file -->
