{% load hospital_stats %}

<!-- Middle Column - Hospital Info -->
<div class="card shadow-sm h-100 border-primary border-opacity-25" id="middle-column" style="border: 1px solid #444 !important;">
    <div class="card-header d-flex justify-content-between align-items-center p-2 bg-info">
        <h4 class="mb-0 text-white">병원 정보</h4>
        <button id="toggle-last-column" class="btn btn-sm btn-light" title="방문 상세 정보 숨기기/보이기">
            <i class="fas fa-chevron-right"></i> 상세
        </button>
    </div>

    <div class="card-body h-100 overflow-auto p-0">
        <!-- Hospital Info Card -->
        <div class="hospital-info-card">
            <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                <h2 class="h4 mb-0">🏥 병원 정보</h2>
                <a href="{% url 'edit_hospital' selected_visit.location_name.id %}" 
                   class="btn btn-outline-primary">
                    <i class="fas fa-edit"></i> 수정
                </a>
            </div>

            <div class="p-3">
                <div class="table-responsive">
                    <table class="table table-borderless mb-0">
                        <tbody>
                            <tr>
                                <th class="text-muted" style="width: 30%;">의료재단</th>
                                <td>{{ selected_visit.location_name.jedan }}</td>
                            </tr>
                            <tr>
                                <th class="text-muted">지점명 / 지점장</th>
                                <td>
                                    {{ selected_visit.location_name.jedan_branch }}<br>
                                    {{ selected_visit.location_name.jedan_branch.key_person_info }}
                                </td>
                            </tr>
                            <tr>
                                <th class="text-muted">병원명</th>
                                <td>
                                    <a href="{% url 'hospital_report_detail' selected_visit.location_name.id %}" 
                                       class="text-primary fw-bold">
                                        <i class="fas fa-eye"></i> {{ selected_visit.location_name.hospital_name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <th class="text-muted">전화번호</th>
                                <td>{{ selected_visit.location_name.phone_number }}</td>
                            </tr>
                            <tr>
                                <th class="text-muted">주소</th>
                                <td>
                                    {{ selected_visit.location_name.do_address }}
                                    {{ selected_visit.location_name.si_address }}
                                    {{ selected_visit.location_name.last_address }}
                                </td>
                            </tr>
                            <tr>
                                <th class="text-muted">사업자 번호</th>
                                <td>{{ selected_visit.location_name.pan_number }}</td>
                            </tr>
                            <tr>
                                <th class="text-muted">CNV 추가 보고</th>
                                <td>{{ selected_visit.location_name.extra_report|yesno:"허용,거부" }}</td>
                            </tr>
                            <tr>
                                <th class="text-muted">KEYMAN 정보</th>
                                <td>{{ selected_visit.location_name.key_person_info }}</td>
                            </tr>
                            <tr>
                                <th class="text-muted">등록 정보</th>
                                <td>
                                    {{ selected_visit.location_name.added_by|default:"-" }}<br>
                                    {{ selected_visit.location_name.created_at|date:"Y-m-d H:i" }}
                                </td>
                            </tr>
                            <tr>
                                <th class="text-muted">최종수정정보</th>
                                <td>
                                    {{ selected_visit.location_name.last_edited_by|default:"-" }}<br>
                                    {{ selected_visit.location_name.last_edited|date:"Y-m-d H:i" }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Service Statistics Section -->
        {% if selected_visit.location_name.is_hospital %}
        <div class="service-stats-section p-3">
            <div class="alert alert-info mb-3">
                <small><strong>서비스 코드:</strong> NIPT=제노맘, GBN=제노베넷, GFN=제노파인드, PGT=지노브로</small>
            </div>

            <!-- Monthly Stats -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar text-primary"></i> 서비스 정보 (지난달)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>서비스명</th>
                                    <th>NIPT</th>
                                    <th>GFN</th>
                                    <th>GBN</th>
                                    <th>PGT</th>
                                    <th>ORA</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <th>지난달 건수</th>
                                    {% get_previous_month_stats selected_visit.location_name.id as stats %}
                                    <td>{{ stats.nipt|default:"0" }}</td>
                                    <td>{{ stats.genofind|default:"0" }}</td>
                                    <td>{{ stats.genobenet|default:"0" }}</td>
                                    <td>{{ stats.pgt|default:"0" }}</td>
                                    <td>{{ stats.ora|default:"0" }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

                        <!-- Doctor Stats -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-user-md text-primary"></i> 의료진별 서비스 현황  (지난달)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>의료진</th>
                                    <th>NIPT</th>
                                    <th>GFN</th>
                                    <th>GBN</th>
                                    <th>PGT</th>
                                    <th>ORA</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% get_doctor_service_stats selected_visit.location_name.id as doctor_stats %}
                                {% for dept, doctors in doctor_stats.items %}
                                    {% for doctor in doctors %}
                                    <tr>
                                        <td>{{ doctor.name }}</td>
                                        <td>{{ doctor.nipt_count|default:"0" }}</td>
                                        <td>{{ doctor.genofind_count|default:"0" }}</td>
                                        <td>{{ doctor.genobenet_count|default:"0" }}</td>
                                        <td>{{ doctor.pgt_count|default:"0" }}</td>
                                        <td>{{ doctor.ora_count|default:"0" }}</td>
                                    </tr>
                                    {% endfor %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>



            <!-- Total Stats -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie text-primary"></i> 전체 서비스 현황
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>서비스명</th>
                                    <th>NIPT</th>
                                    <th>GFN</th>
                                    <th>GBN</th>
                                    <th>PGT</th>
                                    <th>ORA</th>
                                    <th>총계</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% get_doctor_total_stats selected_visit.location_name.id as total_stats %}
                                {% for doctor in total_stats %}
                                <tr>
                                    <td>{{ doctor.name }}</td>
                                    <td>{{ doctor.nipt_count|default:"0" }}</td>
                                    <td>{{ doctor.genofind_count|default:"0" }}</td>
                                    <td>{{ doctor.genobenet_count|default:"0" }}</td>
                                    <td>{{ doctor.pgt_count|default:"0" }}</td>
                                    <td>{{ doctor.ora_count|default:"0" }}</td>
                                    <td class="fw-bold">{{ doctor.total }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>



            <!-- Price Info -->
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-tags text-primary"></i> 서비스 가격 정보
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>서비스</th>
                                    <th>Lite</th>
                                    <th>Standard</th>
                                    <th>Plus</th>
                                    <th>Twin</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <th>NIPT</th>
                                    <td>
                                        <div class="d-flex justify-content-between">
                                            <span>₩{{ selected_visit.location_name.price_lite|floatformat:0 }}</span>
                                            <small class="text-muted">{{ selected_visit.location_name.code_lite|default:"-" }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-between">
                                            <span>₩{{ selected_visit.location_name.price_std|floatformat:0 }}</span>
                                            <small class="text-muted">{{ selected_visit.location_name.code_std|default:"-" }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-between">
                                            <span>₩{{ selected_visit.location_name.price_plus|floatformat:0 }}</span>
                                            <small class="text-muted">{{ selected_visit.location_name.code_plus|default:"-" }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-between">
                                            <span>₩{{ selected_visit.location_name.price_twin|floatformat:0 }}</span>
                                            <small class="text-muted">{{ selected_visit.location_name.code_twin|default:"-" }}</small>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="alert alert-warning m-3">
            <i class="fas fa-exclamation-triangle"></i> 이 기관은 병원으로 설정된 기관이 아닙니다.
        </div>
        {% endif %}
    </div>
</div>

<!-- JavaScript is handled in the main list_detail.html file -->
