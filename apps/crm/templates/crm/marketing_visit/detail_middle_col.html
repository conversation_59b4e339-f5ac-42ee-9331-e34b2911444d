{% load hospital_stats %}

<!-- Middle Column - Hospital Info -->
<div class="card shadow-sm h-100 border-primary border-opacity-25" id="middle-column">
    <div class="card-header d-flex justify-content-between align-items-center p-3 bg-primary">
        <h4 class="mb-0 text-white fw-bold">
            <i class="fas fa-hospital me-2 text-white"></i>병원 정보
        </h4>

        <a href="{% url 'edit_hospital' selected_visit.location_name.id %}" class="btn btn-outline-light btn-sm shadow-sm">
            <i class="fas fa-edit me-1"></i>수정
        </a>


        <button id="toggle-last-column" class="btn btn-sm btn-light shadow-sm" title="방문 상세 정보 숨기기/보이기">
            <i class="fas fa-chevron-right text-dark"></i> <span class="text-dark fw-bold">상세</span>
        </button>
    </div>

    <div class="card-body h-100 overflow-auto p-0">
        <!-- Hospital Info Card -->
        <div class="hospital-info-card bg-white rounded-3 shadow-sm">
            

            <div class="p-3">
                <div class="table-responsive">
                    <table class="table table-borderless mb-0">
                        <tbody>
                            <tr class="border-bottom border-light">
                                <th class="fw-bold text-dark" style="width: 35%;">
                                    <i class="fas fa-building text-primary me-2"></i>의료재단
                                </th>
                                <td class="text-dark fw-medium">{{ selected_visit.location_name.jedan }}</td>
                            </tr>
                            <tr class="border-bottom border-light">
                                <th class="fw-bold text-dark">
                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>지점명 / 지점장
                                </th>
                                <td class="text-dark fw-medium">
                                    <div class="mb-1">{{ selected_visit.location_name.jedan_branch }}</div>
                                    <small class="text-muted">{{ selected_visit.location_name.jedan_branch.key_person_info }}</small>
                                </td>
                            </tr>
                            <tr class="border-bottom border-light">
                                <th class="fw-bold text-dark">
                                    <i class="fas fa-hospital text-primary me-2"></i>병원명
                                </th>
                                <td class="text-dark fw-medium">
                                    <a href="{% url 'hospital_report_detail' selected_visit.location_name.id %}"
                                       class="text-primary fw-bold text-decoration-none">
                                        <i class="fas fa-external-link-alt me-1"></i>{{ selected_visit.location_name.hospital_name }}
                                    </a>


                                    {% if selected_visit.location_name.number_of_patients > 0 %}
                                    <span class="badge bg-info text-white fs-6">여기 click 하시면 병원 분석 보고서를 볼 수 있습니다.</span>
                                    {% else %}
                                    <span class="badge bg-warning fs-6">  NIPT 검체 없는 기관입니다 .</span>
                                    {% endif %}


                                </td>
                            </tr>
                            <tr class="border-bottom border-light">
                                <th class="fw-bold text-dark">
                                    <i class="fas fa-phone text-primary me-2"></i>전화번호
                                </th>
                                <td class="text-dark fw-medium">
                                    <a href="tel:{{ selected_visit.location_name.phone_number }}" class="text-decoration-none">
                                        {{ selected_visit.location_name.phone_number }}
                                    </a>
                                </td>
                            </tr>
                            <tr class="border-bottom border-light">
                                <th class="fw-bold text-dark">
                                    <i class="fas fa-map-marked-alt text-primary me-2"></i>주소
                                </th>
                                <td class="text-dark fw-medium">
                                    {{ selected_visit.location_name.do_address }}
                                    {{ selected_visit.location_name.si_address }}
                                    {{ selected_visit.location_name.last_address }}
                                </td>
                            </tr>
                            <tr class="border-bottom border-light">
                                <th class="fw-bold text-dark">
                                    <i class="fas fa-id-card text-primary me-2"></i>사업자 번호
                                </th>
                                <td class="text-dark fw-medium">{{ selected_visit.location_name.pan_number }}</td>
                            </tr>
                            <tr class="border-bottom border-light">
                                <th class="fw-bold text-dark">
                                    <i class="fas fa-file-medical text-primary me-2"></i>CNV 추가 보고
                                </th>
                                <td class="text-dark fw-medium">
                                    <span class="badge {% if selected_visit.location_name.extra_report %}bg-success{% else %}bg-warning{% endif %} fs-6">
                                        {{ selected_visit.location_name.extra_report|yesno:"허용,거부" }}
                                    </span>
                                </td>
                            </tr>
                            <tr class="border-bottom border-light">
                                <th class="fw-bold text-dark">
                                    <i class="fas fa-user-tie text-primary me-2"></i>KEYMAN 정보
                                </th>
                                <td class="text-dark fw-medium">{{ selected_visit.location_name.key_person_info }}</td>
                            </tr>
                            <tr class="border-bottom border-light">
                                <th class="fw-bold text-dark">
                                    <i class="fas fa-user-plus text-primary me-2"></i>등록 정보
                                </th>
                                <td class="text-dark fw-medium">
                                    <div class="mb-1">{{ selected_visit.location_name.added_by|default:"-" }}</div>
                                    <small class="text-muted">{{ selected_visit.location_name.created_at|date:"Y-m-d H:i" }}</small>
                                </td>
                            </tr>
                            <tr>
                                <th class="fw-bold text-dark">
                                    <i class="fas fa-edit text-primary me-2"></i>최종수정정보
                                </th>
                                <td class="text-dark fw-medium">
                                    <div class="mb-1">{{ selected_visit.location_name.last_edited_by|default:"-" }}</div>
                                    <small class="text-muted">{{ selected_visit.location_name.last_edited|date:"Y-m-d H:i" }}</small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Service Statistics Section -->
        {% if selected_visit.location_name.is_hospital %}
        <div class="service-stats-section p-3">
            <div class="alert alert-primary mb-4 border-0 shadow-sm" style="background-color: #e3f2fd !important;">
                <div class="d-flex align-items-center">
                    <i class="fas fa-info-circle text-primary me-3 fs-4"></i>
                    <div>
                        <strong class="text-primary fs-6 fw-bold">서비스 코드 안내</strong><br>
                        <span class="text-dark fw-medium fs-6">NIPT=제노맘, GBN=제노베넷, GFN=제노파인드, PGT=지노브로</span>
                    </div>
                </div>
            </div>

            <!-- Monthly Stats -->
            <div class="card mb-4 shadow-sm border-0">
                <div class="card-header bg-gradient text-white border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-chart-bar me-2"></i>서비스 정보 (지난달)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th class="fw-bold text-center">구분</th>
                                    <th class="fw-bold text-center text-primary">NIPT</th>
                                    <th class="fw-bold text-center text-success">GFN</th>
                                    <th class="fw-bold text-center text-warning">GBN</th>
                                    <th class="fw-bold text-center text-info">PGT</th>
                                    <th class="fw-bold text-center text-danger">ORA</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="table-light">
                                    <th class="fw-bold text-dark bg-light">지난달 건수</th>
                                    {% get_previous_month_stats selected_visit.location_name.id as stats %}
                                    <td class="text-center fw-bold text-primary fs-5">{{ stats.nipt|default:"0" }}</td>
                                    <td class="text-center fw-bold text-success fs-5">{{ stats.genofind|default:"0" }}</td>
                                    <td class="text-center fw-bold text-warning fs-5">{{ stats.genobenet|default:"0" }}</td>
                                    <td class="text-center fw-bold text-info fs-5">{{ stats.pgt|default:"0" }}</td>
                                    <td class="text-center fw-bold text-danger fs-5">{{ stats.ora|default:"0" }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Doctor Stats -->
            <div class="card mb-4 shadow-sm border-0">
                <div class="card-header bg-gradient text-white border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-user-md me-2"></i>의료진별 서비스 현황 (지난달)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th class="fw-bold text-center">의료진</th>
                                    <th class="fw-bold text-center text-primary">NIPT</th>
                                    <th class="fw-bold text-center text-success">GFN</th>
                                    <th class="fw-bold text-center text-warning">GBN</th>
                                    <th class="fw-bold text-center text-info">PGT</th>
                                    <th class="fw-bold text-center text-danger">ORA</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% get_doctor_service_stats selected_visit.location_name.id as doctor_stats %}
                                {% for dept, doctors in doctor_stats.items %}
                                    {% for doctor in doctors %}
                                    <tr class="table-light">
                                        <td class="fw-bold text-dark">
                                            <i class="fas fa-user-md text-primary me-2"></i>{{ doctor.name }}
                                        </td>
                                        <td class="text-center fw-bold text-primary">{{ doctor.nipt_count|default:"0" }}</td>
                                        <td class="text-center fw-bold text-success">{{ doctor.genofind_count|default:"0" }}</td>
                                        <td class="text-center fw-bold text-warning">{{ doctor.genobenet_count|default:"0" }}</td>
                                        <td class="text-center fw-bold text-info">{{ doctor.pgt_count|default:"0" }}</td>
                                        <td class="text-center fw-bold text-danger">{{ doctor.ora_count|default:"0" }}</td>
                                    </tr>
                                    {% endfor %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>



            <!-- Total Stats -->
            <div class="card mb-4 shadow-sm border-0">
                <div class="card-header bg-gradient text-white border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-chart-pie me-2"></i>전체 서비스 현황
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th class="fw-bold text-center">의료진</th>
                                    <th class="fw-bold text-center text-primary">NIPT</th>
                                    <th class="fw-bold text-center text-success">GFN</th>
                                    <th class="fw-bold text-center text-warning">GBN</th>
                                    <th class="fw-bold text-center text-info">PGT</th>
                                    <th class="fw-bold text-center text-danger">ORA</th>
                                    <th class="fw-bold text-center bg-dark text-white">총계</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% get_doctor_total_stats selected_visit.location_name.id as total_stats %}
                                {% for doctor in total_stats %}
                                <tr class="table-light">
                                    <td class="fw-bold text-dark">
                                        <i class="fas fa-user-md text-primary me-2"></i>{{ doctor.name }}
                                    </td>
                                    <td class="text-center fw-bold text-primary">{{ doctor.nipt_count|default:"0" }}</td>
                                    <td class="text-center fw-bold text-success">{{ doctor.genofind_count|default:"0" }}</td>
                                    <td class="text-center fw-bold text-warning">{{ doctor.genobenet_count|default:"0" }}</td>
                                    <td class="text-center fw-bold text-info">{{ doctor.pgt_count|default:"0" }}</td>
                                    <td class="text-center fw-bold text-danger">{{ doctor.ora_count|default:"0" }}</td>
                                    <td class="text-center fw-bold bg-dark text-white fs-5">{{ doctor.total }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>



            <!-- Price Info -->
            <div class="card shadow-sm border-0">
                <div class="card-header bg-gradient text-white border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-tags me-2"></i>서비스 가격 정보
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th class="fw-bold text-center">서비스</th>
                                    <th class="fw-bold text-center text-info">Lite</th>
                                    <th class="fw-bold text-center text-primary">Standard</th>
                                    <th class="fw-bold text-center text-warning">Plus</th>
                                    <th class="fw-bold text-center text-success">Twin</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="table-light">
                                    <th class="fw-bold text-dark bg-light text-center">
                                        <i class="fas fa-dna text-primary me-2"></i>NIPT
                                    </th>
                                    <td class="text-center">
                                        <div class="d-flex flex-column align-items-center">
                                            <span class="fw-bold text-success fs-6">₩{{ selected_visit.location_name.price_lite|floatformat:0|default:"-" }}</span>
                                            <small class="text-muted badge bg-light text-dark">{{ selected_visit.location_name.code_lite|default:"-" }}</small>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="d-flex flex-column align-items-center">
                                            <span class="fw-bold text-success fs-6">₩{{ selected_visit.location_name.price_std|floatformat:0|default:"-" }}</span>
                                            <small class="text-muted badge bg-light text-dark">{{ selected_visit.location_name.code_std|default:"-" }}</small>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="d-flex flex-column align-items-center">
                                            <span class="fw-bold text-success fs-6">₩{{ selected_visit.location_name.price_plus|floatformat:0|default:"-" }}</span>
                                            <small class="text-muted badge bg-light text-dark">{{ selected_visit.location_name.code_plus|default:"-" }}</small>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="d-flex flex-column align-items-center">
                                            <span class="fw-bold text-success fs-6">₩{{ selected_visit.location_name.price_twin|floatformat:0|default:"-" }}</span>
                                            <small class="text-muted badge bg-light text-dark">{{ selected_visit.location_name.code_twin|default:"-" }}</small>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="alert alert-warning m-3 border-0 shadow-sm">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle text-warning me-3 fs-3"></i>
                <div>
                    <h6 class="alert-heading mb-1 text-warning fw-bold">병원 정보 없음</h6>
                    <p class="mb-0 text-warning-emphasis">이 기관은 병원으로 설정된 기관이 아닙니다.</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- JavaScript is handled in the main list_detail.html file -->
