{% extends "layouts/base.html" %} {% block extra_css %}
<style>
    /* Base layout styles */
    .row.gx-2 {
        display: flex;
        flex-wrap: nowrap;
        width: 100%;
        margin: 0;
        padding: 0;
    }

    /* Column styles */
    .detail-column {
        height: 1000px;
        overflow-y: auto;
        scrollbar-width: thin;
        border: 1px solid #444 !important;
        border-radius: 0;
        background: #fff;
        box-shadow: none !important;
    }

    /* Card styles */
    .card {
        margin: 0 !important;
        border-radius: 0 !important;
        border: none !important;
        box-shadow: none !important;
        background: transparent;
    }

    /* Table styles */
    .table th {
        font-weight: 600;
        color: #6c757d;
    }

    .table td {
        font-weight: 500;
    }

    tr {
        border-bottom: 1px solid #dee2e6;
    }

    /* Transitions */
    .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-9 {
        transition: all 0.3s ease-in-out;
    }

    /* Responsive styles */
    @media (max-width: 968px) {
        .detail-column {
            height: auto !important;
            margin-bottom: 1rem;
        }

        .card {
            margin-bottom: 1rem;
        }

        .hospital-info-card {
            margin-bottom: 1rem;
        }

        .table th {
            width: 40% !important;
        }

        .service-stats-section .table-responsive {
            margin-bottom: 1rem;
            padding: 0 0.5rem;
        }

        .card-header {
            padding: 0.75rem;
        }

        .card-body {
            padding: 0.75rem;
        }

        .table td, .table th {
            padding: 0.5rem;
            font-size: 0.9rem;
        }

        .d-flex.justify-content-between {
            flex-direction: column;
        }

        .d-flex.justify-content-between small {
            margin-top: 0.25rem;
        }

        .service-stats-section .table-responsive table {
            margin: 0 0.5rem;
        }

        .service-stats-section .table-responsive table td {
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }
    }
</style>
{% endblock %} {% block content %}

<!-- Page Header -->
{% include "crm/marketing_visit/marketing_visit_header.html" %}

<!-- Three Column Layout -->
<div class="container-fluid px-2 px-md-4" style="border: 5px solid red !important;">
    <div class="row gx-2">
        <!-- Left Column -->
        <div class="col-12 col-lg-3 card shadow-sm detail-column" style="flex: 0 0 15%; min-width: 180px; border: 1px solid #444 !important; ">
            <div class="card-body">
                {% include "crm/marketing_visit/hospital_visit_list_past.html" with visits=all_visits_in_same_place %}
            </div>
        </div>

        <!-- Middle Column - Hospital Info -->
        <div id="middle-column" class="col-12 col-lg-6 card shadow-sm detail-column" style="flex: 3; min-width: 0; border: 1px solid #444 !important; "  >
            {% include "crm/marketing_visit/detail_middle_col.html" %}
        </div>

        <!-- Right Column -->
        <div id="right-column" class="col-12 col-lg-3 card shadow-sm detail-column" style="flex: 2; min-width: 0; border: 1px solid #444 !important; ">
            {% include "crm/marketing_visit/detail_last_col.html" with visit=selected_visit %}
        </div>
    </div>
</div>

{% block extra_js %}
<div class="container-fluid px-2 px-md-4 mt-3">
    <div class="row">
        <div class="col-12">
            {% if can_edit %}
            <a href="{% url 'marketing_visit_update' selected_visit.pk %}" class="btn btn-primary">
                <i class="fas fa-edit"></i> 수정
            </a>
            {% endif %}
        </div>
    </div>
</div>
{{ block.super }}
{% endblock %}
{% endblock %}
