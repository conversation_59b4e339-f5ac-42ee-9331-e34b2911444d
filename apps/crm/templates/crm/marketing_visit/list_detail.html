{% extends "layouts/base.html" %}

{% block extra_css %}
<style>
    /* Base layout styles */
    .container-fluid.vh-100 {
        height: 100vh;
        overflow: hidden;
    }

    .row.h-100 {
        height: 100%;
        margin: 0;
        display: flex;
        flex-wrap: nowrap;
    }

    /* Column styles */
    .col-12.col-lg-3 {
        height: 100%;
        padding: 0.5rem;
        flex: 0 0 25%;
    }

    /* Card styles */
    .card {
        height: 100%;
        margin: 0;
        border: 1px solid #444 !important;
        border-radius: 0;
        box-shadow: none !important;
    }

    .card-body {
        padding: 0;
        overflow-y: auto;
    }

    /* Column specific styles */
    #middle-column {
        height: 100%;
        transition: all 0.3s ease-in-out;
        overflow-y: auto;
        flex: 0 0 40%;
    }

    #middle-column.collapsed {
        display: none;
    }

    #right-column {
        height: 100%;
        transition: all 0.3s ease-in-out;
        overflow-y: auto;
        flex: 0 0 35%;
    }

    /* Toggle button */
    #toggle-middle-column {
        width: 32px;
        height: 32px;
        padding: 0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease-in-out;
        margin-left: 0.5rem;
    }

    #toggle-middle-column:hover {
        transform: scale(1.1);
        background: #f8f9fa;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Responsive styles */
    @media (max-width: 991px) {
        .container-fluid {
            height: auto !important;
            overflow: auto;
        }

        .row {
            height: auto !important;
            flex-wrap: wrap;
        }

        .col-12 {
            height: auto !important;
            flex: 0 0 100% !important;
        }

        .card {
            margin-bottom: 1rem;
        }

        #middle-column, #right-column {
            margin-bottom: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
{% include "crm/marketing_visit/marketing_visit_header.html" %}

<!-- Three Column Layout -->
<div class="container-fluid vh-100 px-0">
    <div class="d-flex h-100 mx-0">
        <!-- Left Column - Visit List -->
        <div class="h-100 px-2" style="flex: 0 0 25%; min-width: 300px;">
            <div class="card shadow-sm h-100 border-primary border-opacity-25">
                <div class="card-body d-flex flex-column p-0">
                    <!-- Search and Filter Section -->
                    {% include "crm/marketing_visit/marketin_visit_search_filter.html" %}

                    <!-- Visits List -->
                    {% include "crm/marketing_visit/hospital_visit_list_current.html" %}

                    <!-- Pagination -->
                    {% include "crm/marketing_visit/hospital_visit_pagination.html" %}
                </div>

                <!-- Past Visits List -->
                <div id="past-visit-list">
                    {% include "crm/marketing_visit/hospital_visit_list_past.html" with visits=all_visits_in_same_place %}
                </div>
            </div>
        </div>

        <!-- Middle Column - Hospital Info -->
        <div id="middle-column" class="col-12 h-100 px-2" style="flex: 0 0 40%;">
            <div class="card shadow-sm h-100">
                {% if selected_visit %}
                {% include "crm/marketing_visit/detail_middle_col.html" with visit=selected_visit %}
                {% endif %}
            </div>
        </div>

        <!-- Right Column - Visit Details -->
        <div id="right-column" class="col-12 h-100 px-2" style="flex: 0 0 35%;">
            <div class="card shadow-sm h-100">
                {% if selected_visit %}
                <div class="card-header d-flex justify-content-between align-items-center p-2">
                    <h4 class="mb-0">
                        <i class="fas fa-info-circle text-primary"></i> 방문 상세 정보
                    </h4>
                    <button id="toggle-middle-column" class="btn btn-sm btn-outline-secondary" title="병원 정보 숨기기/보이기">
                        <i class="fas fa-chevron-left"></i> 상세
                    </button>
                </div>
                <div class="card-body">
                    {% include "crm/marketing_visit/detail_last_col.html" with selected_visit=selected_visit %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% block javascripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get all necessary elements
    const toggleMiddleButton = document.getElementById('toggle-middle-column');
    const toggleLastButton = document.getElementById('toggle-last-column');
    const middleColumn = document.getElementById('middle-column');
    const rightColumn = document.getElementById('right-column');
    
    // Load saved states from localStorage
    let isMiddleCollapsed = localStorage.getItem('middleColumnCollapsed') === 'true';
    let isLastCollapsed = localStorage.getItem('lastColumnCollapsed') === 'true';
    
    function updateColumnLayout() {
        // Calculate available width (total minus left column which is 25%)
        const availableWidth = 75; // 100% - 25% (left column)

        // Middle column state
        if (isMiddleCollapsed) {
            middleColumn.style.display = 'none';
            middleColumn.style.flex = '0 0 0%';
            // When middle is collapsed, right column takes full available width
            rightColumn.style.flex = `0 0 ${availableWidth}%`;
            if (toggleMiddleButton) {
                toggleMiddleButton.innerHTML = '<i class="fas fa-chevron-right"></i> 상세';
            }
        } else {
            middleColumn.style.display = 'block';
            if (isLastCollapsed) {
                // When last is collapsed, middle column takes full available width
                middleColumn.style.flex = `0 0 ${availableWidth}%`;
            } else {
                // Both columns visible - middle takes 40% of available width
                middleColumn.style.flex = '0 0 40%';
            }
            if (toggleMiddleButton) {
                toggleMiddleButton.innerHTML = '<i class="fas fa-chevron-left"></i> 상세';
            }
        }

        // Last column state
        if (isLastCollapsed) {
            rightColumn.style.display = 'none';
            rightColumn.style.flex = '0 0 0%';
            if (!isMiddleCollapsed) {
                // When last is collapsed and middle is visible, middle takes full available width
                middleColumn.style.flex = `0 0 ${availableWidth}%`;
            }
            if (toggleLastButton) {
                const icon = toggleLastButton.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-chevron-left';
                }
            }
        } else {
            rightColumn.style.display = 'block';
            if (isMiddleCollapsed) {
                // When middle is collapsed and last is visible, last takes full available width
                rightColumn.style.flex = `0 0 ${availableWidth}%`;
            } else {
                // Both columns visible - right takes 35% of available width
                rightColumn.style.flex = '0 0 35%';
            }
            if (toggleLastButton) {
                const icon = toggleLastButton.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-chevron-right';
                }
            }
        }
    }
    
    // Initialize layout
    if (middleColumn && rightColumn) {
        updateColumnLayout();
        
        // Middle column toggle
        if (toggleMiddleButton) {
            toggleMiddleButton.addEventListener('click', function() {
                isMiddleCollapsed = !isMiddleCollapsed;
                localStorage.setItem('middleColumnCollapsed', isMiddleCollapsed);
                updateColumnLayout();
            });
        }
        
        // Last column toggle
        if (toggleLastButton) {
            toggleLastButton.addEventListener('click', function() {
                isLastCollapsed = !isLastCollapsed;
                localStorage.setItem('lastColumnCollapsed', isLastCollapsed);
                updateColumnLayout();
            });
        }
    }

    // Function to reinitialize toggle buttons after AJAX load
    function reinitializeToggleButtons() {
        const newToggleMiddleButton = document.getElementById('toggle-middle-column');
        const newToggleLastButton = document.getElementById('toggle-last-column');

        // Reinitialize middle column toggle
        if (newToggleMiddleButton) {
            newToggleMiddleButton.addEventListener('click', function() {
                isMiddleCollapsed = !isMiddleCollapsed;
                localStorage.setItem('middleColumnCollapsed', isMiddleCollapsed);
                updateColumnLayout();
            });
        }

        // Reinitialize last column toggle
        if (newToggleLastButton) {
            newToggleLastButton.addEventListener('click', function() {
                isLastCollapsed = !isLastCollapsed;
                localStorage.setItem('lastColumnCollapsed', isLastCollapsed);
                updateColumnLayout();
            });
        }

        // Apply current layout state
        updateColumnLayout();
    }

    // AJAX load functionality
    document.querySelectorAll('.load-visit-detail').forEach(function(el) {
        el.addEventListener('click', function(e) {
            e.preventDefault();
            const visitId = this.dataset.visitId;
            if (!visitId) return;

            fetch(`/crm/marketing_visit/ajax/detail/?visit_id=${visitId}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('middle-column').innerHTML = data.middle_html;
                document.getElementById('right-column').innerHTML = data.right_html;

                const pastListContainer = document.getElementById('past-visit-list');
                if (data.past_html && pastListContainer) {
                    pastListContainer.innerHTML = data.past_html;
                }

                // Reinitialize toggle buttons after content is loaded
                reinitializeToggleButtons();
            })
            .catch(error => {
                console.error('AJAX load error:', error);
            });
        });
    });
});
</script>
{% endblock %}

{% endblock %}