{% extends "layouts/base.html" %}

{% block extra_css %}
<style>
    /* Enhanced Design Variables */
    :root {
        --primary-color: #4e73df;
        --secondary-color: #858796;
        --success-color: #1cc88a;
        --info-color: #36b9cc;
        --warning-color: #f6c23e;
        --danger-color: #e74a3b;
        --dark-color: #2c3e50;
        --light-color: #f8f9fc;
        --border-color: #e3e6f0;
        --text-dark: #2c3e50;
        --text-muted: #6c757d;
        --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        --shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    }

    /* Base layout styles */
    .container-fluid.vh-100 {
        height: 100vh;
        overflow: hidden;
    }

    .row.h-100 {
        height: 100%;
        margin: 0;
        display: flex;
        flex-wrap: nowrap;
    }

    /* Column styles */
    .col-12.col-lg-3 {
        height: 100%;
        padding: 0.5rem;
        flex: 0 0 25%;
    }

    /* Enhanced Card styles */
    .card {
        height: 100%;
        margin: 0;
        border: 1px solid var(--border-color) !important;
        border-radius: 0.75rem !important;
        box-shadow: var(--shadow-sm) !important;
        transition: all 0.3s ease;
        background: white;
    }

    .card:hover {
        box-shadow: var(--shadow) !important;
        transform: translateY(-2px);
    }

    .card-body {
        padding: 1.25rem !important;
        overflow-y: auto;
    }

    /* Column specific styles */
    #middle-column {
        height: 100%;
        transition: all 0.3s ease-in-out;
        overflow-y: auto;
        flex: 0 0 40%;
    }

    #middle-column.collapsed {
        display: none;
    }

    #right-column {
        height: 100%;
        transition: all 0.3s ease-in-out;
        overflow-y: auto;
        flex: 0 0 35%;
    }

    /* Toggle button */
    #toggle-middle-column {
        width: 32px;
        height: 32px;
        padding: 0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease-in-out;
        margin-left: 0.5rem;
    }

    #toggle-middle-column:hover {
        transform: scale(1.1);
        background: #f8f9fa;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Enhanced Table Styling */
    .table th {
        font-weight: 700 !important;
        color: var(--text-dark) !important;
        font-size: 1rem !important;
        background-color: #f8f9fc !important;
        border-bottom: 2px solid var(--border-color) !important;
        padding: 1rem 0.75rem !important;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .table td {
        color: var(--text-dark) !important;
        font-size: 0.95rem !important;
        padding: 0.875rem 0.75rem !important;
        vertical-align: middle;
        border-bottom: 1px solid #e9ecef !important;
        font-weight: 500;
    }

    .table-borderless th {
        font-weight: 700 !important;
        color: var(--text-dark) !important;
        font-size: 0.95rem !important;
        background-color: transparent !important;
        border: none !important;
        padding: 0.75rem 0.5rem !important;
        width: 35% !important;
        text-transform: none;
        letter-spacing: 0.3px;
    }

    .table-borderless td {
        color: var(--text-dark) !important;
        font-size: 0.95rem !important;
        padding: 0.75rem 0.5rem !important;
        border: none !important;
        font-weight: 500;
    }

    /* Enhanced Card Header Styling */
    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-bottom: 1px solid var(--border-color) !important;
        border-radius: 0.75rem 0.75rem 0 0 !important;
        padding: 1rem 1.25rem !important;
    }

    .card-header.bg-gradient {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%) !important;
    }

    .card-header h4, .card-header h5 {
        color: white !important;
        font-weight: 600 !important;
        margin-bottom: 0 !important;
        font-size: 1.1rem !important;
    }

    /* Enhanced Badge Styling */
    .badge {
        font-size: 0.8rem !important;
        padding: 0.5em 0.8em !important;
        border-radius: 0.5rem !important;
        font-weight: 600 !important;
    }

    .bg-light.text-dark {
        background-color: #f8f9fa !important;
        color: #495057 !important;
        border: 1px solid #dee2e6 !important;
    }

    /* Enhanced Card Header Colors */
    .card-header.bg-primary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%) !important;
    }

    .card-header.bg-success {
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%) !important;
    }

    .card-header.bg-warning {
        background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%) !important;
    }

    .card-header.bg-info {
        background: linear-gradient(135deg, #36b9cc 0%, #258391 100%) !important;
    }

    .card-header.bg-secondary {
        background: linear-gradient(135deg, #858796 0%, #60616f 100%) !important;
    }

    /* Enhanced List Group Items */
    .list-group-item {
        border: none !important;
        border-bottom: 1px solid #e9ecef !important;
        transition: all 0.2s ease;
    }

    .list-group-item:hover {
        background-color: rgba(78, 115, 223, 0.05) !important;
        transform: translateX(3px);
    }

    /* Enhanced Badge Styling */
    .badge.bg-light {
        background-color: #f8f9fa !important;
        color: #495057 !important;
        border: 1px solid #dee2e6 !important;
        font-weight: 600 !important;
    }

    .badge.bg-warning {
        background-color: #fff3cd !important;
        color: #856404 !important;
        border: 1px solid #ffeaa7 !important;
    }

    /* Reduce excessive margins in third column */
    .visit-details .card {
        margin-bottom: 1rem !important;
    }

    .visit-details .card-body {
        padding: 1rem !important;
    }

    /* Enhanced Text Readability */
    .text-dark {
        color: #2c3e50 !important;
    }

    .fw-medium {
        font-weight: 500 !important;
    }

    /* Enhanced File Icons */
    .fas.fa-file {
        color: #dc3545 !important;
    }

    .fas.fa-folder {
        color: #ffc107 !important;
    }

    /* Soft Color Pattern for Consistent Headers */
    .soft-primary-bg {
        background-color: #e3f2fd !important; /* Light blue like service code alert */
    }

    .soft-success-bg {
        background-color: #e8f5e8 !important; /* Light green */
    }

    .soft-warning-bg {
        background-color: #fff3cd !important; /* Light yellow */
    }

    .soft-info-bg {
        background-color: #d1ecf1 !important; /* Light cyan */
    }

    .soft-secondary-bg {
        background-color: #e2e3e5 !important; /* Light gray */
    }

    /* Consistent Header Pattern */
    .card-header.soft-bg {
        border: none !important;
        padding: 1rem 1.25rem !important;
    }

    .card-header.soft-bg h5 {
        color: #2c3e50 !important;
        font-weight: 700 !important;
        margin-bottom: 0 !important;
        font-size: 1rem !important;
    }

    /* Enhanced Button Styling */
    .btn {
        border-radius: 0.5rem !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem !important;
    }

    .btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%) !important;
        border: none !important;
    }

    .btn-outline-secondary {
        border: 2px solid var(--secondary-color) !important;
        color: var(--secondary-color) !important;
        background: transparent !important;
    }

    .btn-outline-secondary:hover {
        background: var(--secondary-color) !important;
        color: white !important;
    }

    /* Enhanced Alert Styling */
    .alert {
        border-radius: 0.75rem !important;
        border: none !important;
        font-weight: 500 !important;
        box-shadow: var(--shadow-sm) !important;
    }

    .alert-info {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
        color: #1565c0 !important;
    }

    /* Enhanced Typography */
    h1, h2, h3, h4, h5, h6 {
        color: var(--text-dark) !important;
        font-weight: 600 !important;
    }

    .text-muted {
        color: var(--text-muted) !important;
        font-weight: 500 !important;
    }

    /* Price Display Enhancement */
    .table td span {
        font-weight: 600 !important;
        color: var(--success-color) !important;
        font-size: 1rem !important;
    }

    /* Enhanced Hover Effects */
    .table-hover tbody tr:hover {
        background-color: rgba(78, 115, 223, 0.05) !important;
        transform: scale(1.01);
        transition: all 0.2s ease;
    }

    /* Enhanced Link Styling */
    a {
        color: var(--primary-color) !important;
        text-decoration: none !important;
        font-weight: 500 !important;
        transition: all 0.3s ease;
    }

    a:hover {
        color: #224abe !important;
        text-decoration: underline !important;
    }

    /* Enhanced Icon Styling */
    .fas, .far {
        margin-right: 0.5rem;
        color: var(--primary-color);
    }

    /* Responsive styles */
    @media (max-width: 991px) {
        .container-fluid {
            height: auto !important;
            overflow: auto;
        }

        .row {
            height: auto !important;
            flex-wrap: wrap;
        }

        .col-12 {
            height: auto !important;
            flex: 0 0 100% !important;
        }

        .card {
            margin-bottom: 1rem;
        }

        #middle-column, #right-column {
            margin-bottom: 1rem;
        }
    }

    /* Mobile Responsive Design */
    @media (max-width: 768px) {
        .container-fluid {
            padding: 0 !important;
        }

        .d-flex {
            flex-direction: column !important;
        }

        .h-100 {
            height: auto !important;
            min-height: 50vh;
        }

        .px-2 {
            padding-left: 0.5rem !important;
            padding-right: 0.5rem !important;
        }

        .card-body {
            padding: 1rem !important;
        }

        .table th, .table td {
            padding: 0.5rem 0.25rem !important;
            font-size: 0.8rem !important;
        }

        .table-borderless th, .table-borderless td {
            padding: 0.5rem 0.25rem !important;
            font-size: 0.85rem !important;
        }

        .btn {
            font-size: 0.8rem !important;
            padding: 0.5rem 1rem !important;
        }

        .card-header h4, .card-header h5 {
            font-size: 1rem !important;
        }
    }

    /* Very Small Mobile Screens */
    @media (max-width: 576px) {
        .table-responsive {
            font-size: 0.75rem;
        }

        .table th, .table td {
            padding: 0.375rem 0.25rem !important;
            font-size: 0.75rem !important;
        }

        .card-header {
            padding: 0.75rem 1rem !important;
        }

        .card-body {
            padding: 0.75rem !important;
        }

        .table-borderless th {
            font-size: 0.8rem !important;
        }

        .table-borderless td {
            font-size: 0.8rem !important;
        }
    }

    /* Tablet Responsive Design */
    @media (min-width: 769px) and (max-width: 1024px) {
        .table th, .table td {
            font-size: 0.85rem !important;
            padding: 0.75rem 0.5rem !important;
        }

        .card-header h4, .card-header h5 {
            font-size: 1rem !important;
        }
    }
</style>
{% endblock %}

{% block content %}
{% include "crm/marketing_visit/marketing_visit_header.html" %}

<!-- Three Column Layout -->
<div class="container-fluid vh-100 px-0">
    <div class="d-flex h-100 mx-0">
        <!-- Left Column - Visit List -->
        <div class="h-100 px-2" style="flex: 0 0 25%; min-width: 300px;">
            <div class="card shadow-sm h-100 border-primary border-opacity-25">
                <div class="card-body d-flex flex-column p-0">
                    <!-- Search and Filter Section -->
                    {% include "crm/marketing_visit/marketin_visit_search_filter.html" %}

                    <!-- Visits List -->
                    {% include "crm/marketing_visit/hospital_visit_list_current.html" %}

                    <!-- Pagination -->
                    {% include "crm/marketing_visit/hospital_visit_pagination.html" %}
                </div>

                <!-- Past Visits List -->
                <div id="past-visit-list">
                    {% include "crm/marketing_visit/hospital_visit_list_past.html" with visits=all_visits_in_same_place %}
                </div>
            </div>
        </div>

        <!-- Middle Column - Hospital Info -->
        <div id="middle-column" class="col-12 h-100 px-2" style="flex: 0 0 40%; border: 1px solid #444 !important;">
            <div class="card shadow-sm h-100">
                {% if selected_visit %}
                {% include "crm/marketing_visit/detail_middle_col.html" with visit=selected_visit %}
                {% endif %}
            </div>
        </div>

        <!-- Right Column - Visit Details -->
        <div id="right-column" class="col-12 h-100 px-2" style="flex: 0 0 35%;">
            <div class="card shadow-sm h-100">
                {% if selected_visit %}
                <div class="card-header d-flex justify-content-between align-items-center p-3 bg-primary">
                    <button id="toggle-middle-column" class="btn btn-sm btn-light shadow-sm" title="병원 정보 숨기기/보이기">
                        <i class="fas fa-chevron-left text-dark"></i> <span class="text-dark fw-bold">상세</span>
                    </button>
                    
                    <h4 class="mb-0 text-white fw-bold">
                        <i class="fas fa-info-circle me-2 text-white"></i>방문 상세 정보
                    </h4>

                    <div class="d-flex gap-2">
                        {% if request.user.is_superuser %}
                        <a href="{% url 'marketing_visit_update' selected_visit.id %}" class="btn btn-sm btn-light shadow-sm">
                            <i class="fas fa-edit me-1 text-dark"></i><span class="text-dark fw-bold">수정</span>
                        </a>
                        {% endif %}
                    </div>



          






                    
                </div>
                <div class="card-body p-0">
                    {% include "crm/marketing_visit/detail_last_col.html" with selected_visit=selected_visit %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% block javascripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get all necessary elements
    const toggleMiddleButton = document.getElementById('toggle-middle-column');
    const toggleLastButton = document.getElementById('toggle-last-column');
    const middleColumn = document.getElementById('middle-column');
    const rightColumn = document.getElementById('right-column');
    
    // Load saved states from localStorage
    let isMiddleCollapsed = localStorage.getItem('middleColumnCollapsed') === 'true';
    let isLastCollapsed = localStorage.getItem('lastColumnCollapsed') === 'true';
    
    function updateColumnLayout() {
        // Calculate available width (total minus left column which is 25%)
        const availableWidth = 75; // 100% - 25% (left column)

        // Middle column state
        if (isMiddleCollapsed) {
            middleColumn.style.display = 'none';
            middleColumn.style.flex = '0 0 0%';
            // When middle is collapsed, right column takes full available width
            rightColumn.style.flex = `0 0 ${availableWidth}%`;
            if (toggleMiddleButton) {
                toggleMiddleButton.innerHTML = '<i class="fas fa-chevron-right"></i> 상세';
            }
        } else {
            middleColumn.style.display = 'block';
            if (isLastCollapsed) {
                // When last is collapsed, middle column takes full available width
                middleColumn.style.flex = `0 0 ${availableWidth}%`;
            } else {
                // Both columns visible - middle takes 40% of available width
                middleColumn.style.flex = '0 0 40%';
            }
            if (toggleMiddleButton) {
                toggleMiddleButton.innerHTML = '<i class="fas fa-chevron-left"></i> 상세';
            }
        }

        // Last column state
        if (isLastCollapsed) {
            rightColumn.style.display = 'none';
            rightColumn.style.flex = '0 0 0%';
            if (!isMiddleCollapsed) {
                // When last is collapsed and middle is visible, middle takes full available width
                middleColumn.style.flex = `0 0 ${availableWidth}%`;
            }
            if (toggleLastButton) {
                const icon = toggleLastButton.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-chevron-left';
                }
            }
        } else {
            rightColumn.style.display = 'block';
            if (isMiddleCollapsed) {
                // When middle is collapsed and last is visible, last takes full available width
                rightColumn.style.flex = `0 0 ${availableWidth}%`;
            } else {
                // Both columns visible - right takes 35% of available width
                rightColumn.style.flex = '0 0 35%';
            }
            if (toggleLastButton) {
                const icon = toggleLastButton.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-chevron-right';
                }
            }
        }
    }
    
    // Initialize layout
    if (middleColumn && rightColumn) {
        updateColumnLayout();
        
        // Middle column toggle
        if (toggleMiddleButton) {
            toggleMiddleButton.addEventListener('click', function() {
                isMiddleCollapsed = !isMiddleCollapsed;
                localStorage.setItem('middleColumnCollapsed', isMiddleCollapsed);
                updateColumnLayout();
            });
        }
        
        // Last column toggle
        if (toggleLastButton) {
            toggleLastButton.addEventListener('click', function() {
                isLastCollapsed = !isLastCollapsed;
                localStorage.setItem('lastColumnCollapsed', isLastCollapsed);
                updateColumnLayout();
            });
        }
    }

    // Function to reinitialize toggle buttons after AJAX load
    function reinitializeToggleButtons() {
        const newToggleMiddleButton = document.getElementById('toggle-middle-column');
        const newToggleLastButton = document.getElementById('toggle-last-column');

        // Reinitialize middle column toggle
        if (newToggleMiddleButton) {
            newToggleMiddleButton.addEventListener('click', function() {
                isMiddleCollapsed = !isMiddleCollapsed;
                localStorage.setItem('middleColumnCollapsed', isMiddleCollapsed);
                updateColumnLayout();
            });
        }

        // Reinitialize last column toggle
        if (newToggleLastButton) {
            newToggleLastButton.addEventListener('click', function() {
                isLastCollapsed = !isLastCollapsed;
                localStorage.setItem('lastColumnCollapsed', isLastCollapsed);
                updateColumnLayout();
            });
        }

        // Apply current layout state
        updateColumnLayout();
    }

    // AJAX load functionality
    document.querySelectorAll('.load-visit-detail').forEach(function(el) {
        el.addEventListener('click', function(e) {
            e.preventDefault();
            const visitId = this.dataset.visitId;
            if (!visitId) return;

            fetch(`/crm/marketing_visit/ajax/detail/?visit_id=${visitId}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('middle-column').innerHTML = data.middle_html;
                document.getElementById('right-column').innerHTML = data.right_html;

                const pastListContainer = document.getElementById('past-visit-list');
                if (data.past_html && pastListContainer) {
                    pastListContainer.innerHTML = data.past_html;
                }

                // Reinitialize toggle buttons after content is loaded
                reinitializeToggleButtons();
            })
            .catch(error => {
                console.error('AJAX load error:', error);
            });
        });
    });
});
</script>
{% endblock %}

{% endblock %}