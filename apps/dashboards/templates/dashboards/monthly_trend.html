{% extends "layouts/base.html" %}

{% block title %} 월간 트렌드 분석 {% endblock %}

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}
<style>
    .dashboard-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }
    
    .dashboard-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }
    
    .dashboard-card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 1rem;
        border-radius: 8px 8px 0 0;
    }
    
    .dashboard-card-body {
        padding: 1.5rem;
    }
    
    .dashboard-stat-card {
        background: linear-gradient(45deg, #4e73df, #224abe);
        color: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .dashboard-stat-card h3 {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        opacity: 0.8;
    }
    
    .dashboard-stat-card .stat-value {
        font-size: 2rem;
        font-weight: bold;
    }
    
    .dashboard-stat-card .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
    }
    
    .dashboard-chart-container {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .dashboard-export-btn {
        background: #4e73df;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .dashboard-export-btn:hover {
        background: #224abe;
        transform: translateY(-1px);
    }
    
    .dashboard-export-btn i {
        margin-right: 0.5rem;
    }
    
    .dashboard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    
    .dashboard-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: #2e59d9;
        margin: 0;
    }
    
    .dashboard-actions {
        display: flex;
        gap: 1rem;
    }
    
    .dashboard-filter {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .dashboard-filter select {
        padding: 0.5rem;
        border: 1px solid #d1d3e2;
        border-radius: 4px;
        min-width: 150px;
    }
    
    .dashboard-chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .dashboard-chart-title {
        font-size: 1.2rem;
        font-weight: bold;
        color: #2e59d9;
        margin: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">월간 트렌드 분석</h1>
    </div>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'monthly_trend' %}">분석</a></li>
            <li class="breadcrumb-item active" aria-current="page">월간 트렌드</li>
        </ol>
    </nav>

    <!-- Include Date Range Form -->
    {% include 'dashboards/components/date_range_form.html' %}

    <!-- Dashboard Stats -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-stat-card">
                <h3>월간 총 검체 수</h3>
                <div class="stat-value">{{ total_samples|default:"0" }}</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-stat-card">
                <h3>월 평균 검체 수</h3>
                <div class="stat-value">{{ avg_monthly|default:"0" }}</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-stat-card">
                <h3>최고 월 검체 수</h3>
                <div class="stat-value">{{ max_monthly|default:"0" }}</div>
                <div class="stat-label">{{ max_monthly_name|default:"" }}</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-stat-card">
                <h3>최저 월 검체 수</h3>
                <div class="stat-value">{{ min_monthly|default:"0" }}</div>
                <div class="stat-label">{{ min_monthly_name|default:"" }}</div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="row">
        <!-- Jedan Trend -->
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="dashboard-chart-header">
                        <h5 class="dashboard-chart-title">제단별 월간 트렌드</h5>
                        <a href="?export=jedan_trend{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" 
                           class="dashboard-export-btn">
                            <i class="fas fa-download"></i> 내보내기
                        </a>
                    </div>
                </div>
                <div class="dashboard-card-body">
                    {{ jedan_div|safe }}
                </div>
            </div>
        </div>

        <!-- Service Type Trend -->
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="dashboard-chart-header">
                        <h5 class="dashboard-chart-title">서비스 유형별 월간 트렌드</h5>
                        <a href="?export=service_trend{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" 
                           class="dashboard-export-btn">
                            <i class="fas fa-download"></i> 내보내기
                        </a>
                    </div>
                </div>
                <div class="dashboard-card-body">
                    {{ service_div|safe }}
                </div>
            </div>
        </div>

        <!-- Fetus Trend -->
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="dashboard-chart-header">
                        <h5 class="dashboard-chart-title">태아 정보별 월간 트렌드</h5>
                        <a href="?export=fetus_trend{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" 
                           class="dashboard-export-btn">
                            <i class="fas fa-download"></i> 내보내기
                        </a>
                    </div>
                </div>
                <div class="dashboard-card-body">
                    {{ fetus_div|safe }}
                </div>
            </div>
        </div>

        <!-- Hospital Trend -->
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="dashboard-chart-header">
                        <h5 class="dashboard-chart-title">병원별 월간 트렌드</h5>
                        <a href="?export=hospital_trend{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" 
                           class="dashboard-export-btn">
                            <i class="fas fa-download"></i> 내보내기
                        </a>
                    </div>
                </div>
                <div class="dashboard-card-body">
                    {{ hospital_div|safe }}
                </div>
            </div>
        </div>

        <!-- Release Trend -->
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="dashboard-chart-header">
                        <h5 class="dashboard-chart-title">출고 현황 월간 트렌드</h5>
                        <a href="?export=release_trend{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" 
                           class="dashboard-export-btn">
                            <i class="fas fa-download"></i> 내보내기
                        </a>
                    </div>
                </div>
                <div class="dashboard-card-body">
                    {{ release_div|safe }}
                </div>
            </div>
        </div>

        <!-- Result Trend -->
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="dashboard-chart-header">
                        <h5 class="dashboard-chart-title">결과별 월간 트렌드</h5>
                        <a href="?export=result_trend{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" 
                           class="dashboard-export-btn">
                            <i class="fas fa-download"></i> 내보내기
                        </a>
                    </div>
                </div>
                <div class="dashboard-card-body">
                    {{ final_result_div|safe }}
                </div>
            </div>
        </div>

        <!-- Test Type Trend -->
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="dashboard-chart-header">
                        <h5 class="dashboard-chart-title">테스트 유형별 월간 트렌드 (모든 샘플)</h5>
                        <a href="?export=test_type_trend{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" 
                           class="dashboard-export-btn">
                            <i class="fas fa-download"></i> 내보내기
                        </a>
                    </div>
                </div>
                <div class="dashboard-card-body">
                    {{ test_type_div|safe }}
                </div>
            </div>
        </div>

        <!-- Result Status Trend -->
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="dashboard-chart-header">
                        <h5 class="dashboard-chart-title">서비스 vs 비서비스 데이터 월간 트렌드</h5>
                        <a href="?export=result_status_trend{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" 
                           class="dashboard-export-btn">
                            <i class="fas fa-download"></i> 내보내기
                        </a>
                    </div>
                </div>
                <div class="dashboard-card-body">
                    {{ result_status_div|safe }}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<script>
    // Add any additional JavaScript functionality here
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize any charts or interactive elements
    });
</script>
{% endblock %}
