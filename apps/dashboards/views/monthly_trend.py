import os
import sys
import django
import random
from datetime import date, timedelta
from django.shortcuts import render
import pandas as pd

import plotly.express as px
from plotly.offline import plot

from apps.genomom.models import SampleInfo
from apps.authentication.decorators import admin_required, staff_required
from django.contrib.auth.decorators import login_required
from django.shortcuts import render
from django.http import HttpResponse
import io
import numpy as np


#! Later change extraction_date to patient_created_at
config = dict({'scrollZoom': False, 'displayModeBar': False})
width, height = 1900, 800


def export_to_excel(df, filename):
    """Helper function to export DataFrame to Excel"""
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='Sheet1', index=False)
    output.seek(0)
    response = HttpResponse(
        output.read(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    return response


def generate_bar_plot(df, target_col, width, height, config, horizontal_line=True, legend=True, show_trend=False):
    """Generate a bar plot with trend line and improved styling"""
    # Group by year and month
    grouped_data = df.groupby(['yy_mm', target_col]).agg({
        'Counts': 'sum',
    }).reset_index()
    
    grouped_data.sort_values(["yy_mm", target_col], inplace=True)
    
    # Calculate percentages for each group
    total_by_month = grouped_data.groupby('yy_mm')['Counts'].transform('sum')
    grouped_data['Percentage'] = (grouped_data['Counts'] / total_by_month * 100).round(1)
    
    # Create the base bar plot
    fig = px.bar(grouped_data, x='yy_mm', y="Counts",
                 color=target_col,
                 text=grouped_data['Percentage'].apply(lambda x: f'{x}%'),
                 labels={'Counts': 'NIPT 건수', 
                        'yy_mm': '월',
                        target_col: '구분'},
                 color_discrete_sequence=px.colors.qualitative.Plotly)
    
    # Add horizontal line if requested and show_trend is True
    if horizontal_line and show_trend:
        # Calculate average monthly count
        avg_monthly = grouped_data.groupby('yy_mm')['Counts'].sum().mean()
        fig.add_shape(
            type='line',
            x0=fig.data[0].x.min(),
            x1=fig.data[0].x.max(),
            y0=avg_monthly,
            y1=avg_monthly,
            line=dict(color='#000080', width=3, dash='dash'),
            name='월 평균'
        )
    
    # Calculate and add trend line only if show_trend is True
    if show_trend:
        all_samples_counts = grouped_data.groupby('yy_mm').sum("Counts").reset_index()
        all_samples_counts.sort_values("yy_mm", inplace=True)
        
        x = range(len(all_samples_counts["yy_mm"]))
        y = all_samples_counts["Counts"]
        z = np.polyfit(x, y, 1)
        p = np.poly1d(z)
        
        # Calculate R-squared value
        y_pred = p(x)
        r_squared = 1 - (np.sum((y - y_pred) ** 2) / np.sum((y - np.mean(y)) ** 2))
        
        # Add trend line
        fig.add_scatter(
            x=all_samples_counts["yy_mm"],
            y=p(x),
            mode='lines',
            name=f'추세선 (R² = {r_squared:.3f})',
            line=dict(
                color='red',
                width=3,
                dash='dash'
            ),
            hoverinfo='name'
        )
        
        # Add trend line equation
        slope = z[0]
        intercept = z[1]
        equation = f'y = {slope:.2f}x + {intercept:.2f}'
        
        fig.add_annotation(
            x=all_samples_counts["yy_mm"].iloc[-1],
            y=p(len(all_samples_counts["yy_mm"])-1),
            text=equation,
            showarrow=True,
            arrowhead=1,
            ax=50,
            ay=-50,
            font=dict(size=12, color='red')
        )
    
    # Add total counts line
    all_samples_counts = grouped_data.groupby('yy_mm').sum("Counts").reset_index()
    fig.add_scatter(
        x=all_samples_counts["yy_mm"],
        y=all_samples_counts["Counts"],
        mode='lines+markers',
        name="총계",
        line=dict(color='green', width=4)
    )
    
    fig.update_layout(
        width=width,
        height=height,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        showlegend=legend,
        xaxis_title='월',
        yaxis_title='NIPT 건수',
        hovermode='x unified'
    )
    
    return plot(fig, config=config, output_type="div"), grouped_data


# Create your views here.
@login_required(login_url="/")
@staff_required(allowed_user_groups=["7",])  # ! Show to All Staff / Employee
def monthly_trend(request):
    """Monthly trend analysis for NIPT samples"""
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    # Handle export requests
    if request.GET.get('export'):
        export_type = request.GET.get('export')
        if export_type == 'jedan_trend':
            return export_to_excel(jedan_trend_df, 'monthly_jedan_trend.xlsx')
        elif export_type == 'service_trend':
            return export_to_excel(service_trend_df, 'monthly_service_trend.xlsx')
        elif export_type == 'result_trend':
            return export_to_excel(result_trend_df, 'monthly_result_trend.xlsx')
        elif export_type == 'fetus_trend':
            return export_to_excel(fetus_trend_df, 'monthly_fetus_trend.xlsx')
        elif export_type == 'hospital_trend':
            return export_to_excel(hospital_trend_df, 'monthly_hospital_trend.xlsx')
        elif export_type == 'release_trend':
            return export_to_excel(release_trend_df, 'monthly_release_trend.xlsx')
        elif export_type == 'test_type_trend':
            return export_to_excel(test_type_trend_df, 'monthly_test_type_trend.xlsx')
        elif export_type == 'result_status_trend':
            return export_to_excel(result_status_trend_df, 'monthly_result_status_trend.xlsx')

    config = dict({'scrollZoom': False, 'displayModeBar': False})
    
    # Get all samples without filters for test_type graph and result_status graph
    all_samples_unfiltered = SampleInfo.objects.filter(is_active=True)
    
    # Create test_type graph if unfiltered samples exist
    test_type_div = None
    test_type_trend_df = None
    result_status_div = None
    result_status_trend_df = None
    
    if all_samples_unfiltered:
        # Get test_type data
        test_type_samples = all_samples_unfiltered.values(
            "test_type",
            "entry_date"
        )
        
        test_type_df = pd.DataFrame(test_type_samples)
        test_type_df["Counts"] = 1
        
        # Process dates
        test_type_df["entry_date"] = pd.to_datetime(test_type_df.entry_date, format='%Y%m%d', errors='ignore')
        
        # Add year and month columns
        test_type_df['year'] = test_type_df['entry_date'].dt.year
        test_type_df['month'] = test_type_df['entry_date'].dt.month
        test_type_df["yy_mm"] = test_type_df['year'].astype(str) + '년_' + test_type_df['month'].astype(str).str.zfill(2) + '월'
        
        # Generate test_type plot
        test_type_div, test_type_trend_df = generate_bar_plot(test_type_df, 'test_type', width, height, config, horizontal_line=False, show_trend=True)
        
        # Get result_status data
        result_status_samples = all_samples_unfiltered.values(
            "result_status",
            "entry_date"
        )
        
        result_status_df = pd.DataFrame(result_status_samples)
        result_status_df["Counts"] = 1
        
        # Process dates
        result_status_df["entry_date"] = pd.to_datetime(result_status_df.entry_date, format='%Y%m%d', errors='ignore')
        
        # Add year and month columns
        result_status_df['year'] = result_status_df['entry_date'].dt.year
        result_status_df['month'] = result_status_df['entry_date'].dt.month
        result_status_df["yy_mm"] = result_status_df['year'].astype(str) + '년_' + result_status_df['month'].astype(str).str.zfill(2) + '월'
        
        # Convert boolean to more descriptive categories
        result_status_df["result_status"] = result_status_df["result_status"].map({True: "서비스 데이터", False: "비서비스 데이터"})
        
        # Generate result_status plot
        result_status_div, result_status_trend_df = generate_bar_plot(result_status_df, 'result_status', width, height, config, horizontal_line=False, show_trend=True)
    
    # Continue with filtered samples for other graphs
    all_samples = SampleInfo.objects.filter(is_active=True, report_publish=True)
    
    # Apply date filter if dates are provided
    if start_date and end_date:
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            all_samples = all_samples.filter(entry_date__range=[start_date, end_date])
        except ValueError:
            pass

    context = {}
    if all_samples:
        samples = all_samples.values(
            "patient__jedan__jedan_name",
            "patient__jedan_branch__jedan_branch_name",
            "patient__hospital__hospital_name",
            "patient__fetus_number",
            "patient__ivf_treatment",
            "service_type__service_name",
            "entry_date",
            "result_sent_time",
            "report_publish",
            "patient__hospital__do_address",
            "patient__hospital__si_address",
            "samples_trisomy__final_result2"
        )
        
        df = pd.DataFrame(samples)
        df["Counts"] = 1
        
        df.rename(columns={
            'patient__jedan__jedan_name': "jedan",
            'patient__jedan_branch__jedan_branch_name': "jedan_branch",
            'patient__hospital__hospital_name': "hospital",
            'patient__test_type': "test_type",
            "patient__fetus_number": "fetus",
            "patient__ivf_treatment": "ivf_treatment",
            "service_type__service_name": "service_type",
            "samples_trisomy__final_result2": "final_result",
            "patient__hospital__do_address": "do_address",
            "patient__hospital__si_address": "si_address",
        }, inplace=True)
        
        df["final_result"] = df.final_result.replace({
            "0": 'Low', 
            '1': 'High', 
            '2': 'Border', 
            '6': 'Re-Draw', 
            "7": "검사불능"
        })
        
        df.final_result.fillna('미판독', inplace=True)
        df["report_publish"] = df.report_publish.replace({True: "완료", False: "미출고"})
        df["ivf_treatment"] = df["ivf_treatment"].replace({"1": "유", "0": "무", '-': '-'})
        
        # Format hospital names
        df["hospital"] = df["jedan"] + '-' + df["hospital"]
        df["jedan_branch"] = df["jedan"] + '-' + df["jedan_branch"]
        
        # Process dates
        df["entry_date"] = pd.to_datetime(df.entry_date, format='%Y%m%d', errors='ignore')
        df['result_sent_time'] = pd.to_datetime(df.result_sent_time.dt.date)
        
        # Add year and month columns
        df['year'] = df['entry_date'].dt.year
        df['month'] = df['entry_date'].dt.month
        df["yy_mm"] = df['year'].astype(str) + '년_' + df['month'].astype(str).str.zfill(2) + '월'
        
        # Generate plots
        jedan_div, jedan_trend_df = generate_bar_plot(df, 'jedan', width, height, config, show_trend=True)
        service_div, service_trend_df = generate_bar_plot(df, 'service_type', width, height, config, show_trend=False)
        fetus_div, fetus_trend_df = generate_bar_plot(df, 'fetus', width, height, config, horizontal_line=False, show_trend=False)
        hospital_div, hospital_trend_df = generate_bar_plot(df, 'hospital', width, height, config, legend=False, show_trend=False)
        do_address_div, do_address_trend_df = generate_bar_plot(df, 'do_address', width, height, config, legend=False, horizontal_line=False, show_trend=False)
        ivf_treatment_div, ivf_treatment_trend_df = generate_bar_plot(df, 'ivf_treatment', width, height, config, horizontal_line=False, show_trend=False)
        release_div, release_trend_df = generate_bar_plot(df, 'report_publish', width, height, config, horizontal_line=False, show_trend=False)
        final_result_div, result_trend_df = generate_bar_plot(df, 'final_result', width, height, config, horizontal_line=False, show_trend=False)
        
        # Calculate monthly statistics
        monthly_stats = df.groupby('yy_mm')['Counts'].sum().reset_index()
        avg_monthly = monthly_stats['Counts'].mean()
        max_monthly = monthly_stats.loc[monthly_stats['Counts'].idxmax()]
        min_monthly = monthly_stats.loc[monthly_stats['Counts'].idxmin()]
        
        context = {
            "jedan_div": jedan_div,
            "service_div": service_div,
            "fetus_div": fetus_div,
            "hospital_div": hospital_div,
            "do_address_div": do_address_div,
            "ivf_treatment_div": ivf_treatment_div,
            "release_div": release_div,
            "final_result_div": final_result_div,
            "test_type_div": test_type_div,  # Add the test_type graph
            "result_status_div": result_status_div,  # Add the result_status graph
            "avg_monthly": int(avg_monthly),
            "max_monthly": int(max_monthly['Counts']),
            "max_monthly_name": max_monthly['yy_mm'],
            "min_monthly": int(min_monthly['Counts']),
            "min_monthly_name": min_monthly['yy_mm'],
            "total_samples": int(monthly_stats['Counts'].sum()),
            "active_menu": "dashboard",
        }
        
        template = "dashboards/monthly_trend.html"
        return render(request, template_name=template, context=context)
    else:
        template = "dashboards/monthly_trend.html"
        return render(request, template_name=template)
